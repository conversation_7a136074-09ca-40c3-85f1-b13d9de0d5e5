import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CqrsModule } from '@nestjs/cqrs';
import { SqsModule } from '@ssut/nestjs-sqs';
import { CompaniesModule } from 'src/companies/companies.module';
import { CustomersModule } from 'src/customers/customers.module';
import { FilesModule } from 'src/files/files.module';
import { GupshupBillingEventsModule } from 'src/gupshup-billing-events/gupshup-billing-events.module';
import { LogsModule } from 'src/logs/logs.module';
import { MessagesModule } from 'src/messages/messages.module';
import { SqsQueueEnum } from 'src/shared/types/SqsQueueEnum';
import { ShortUrlsModule } from 'src/short-urls/short-urls.module';
import { MessageTemplatesModule } from './../message-templates/message-templates.module';
import { GupshupPartnerService } from './gupshup-partner.service';
import { GupshupWebhookHandlerService } from './gupshup-webhook-handler.service';
import { GupshupController } from './gupshup.controller';
import { GupshupService } from './gupshup.service';
import { ProcessGupshupEventHandler } from './handlers/process-gupshup-event.handler';
import { ConversationsModule } from 'src/conversations/conversations.module';
import { ProductCatalogModule } from 'src/product-catalog/product-catalog.module';
import { AiAgentsModule } from 'src/ai-agents/ai-agents.module';

const consumers =
  process.env.GUPSHUP_USE_AWS_SQS_QUEUE === 'false'
    ? []
    : [
        {
          name: SqsQueueEnum.GUPSHUP_EVENTS,
          queueUrl: process.env.AWS_SQS_GUPSHUP_EVENTS_QUEUE_URL!,
          region: process.env.AWS_REGION,
          // pollingWaitTimeMs: 0,
        },
      ];

export const CommandHandlers = [ProcessGupshupEventHandler];
@Module({
  controllers: [GupshupController],
  providers: [
    GupshupService,
    GupshupPartnerService,
    GupshupWebhookHandlerService,
    ...CommandHandlers,
  ],
  imports: [
    SqsModule.register({
      consumers,
    }),
    forwardRef(() => MessageTemplatesModule),
    forwardRef(() => MessagesModule),
    forwardRef(() => CompaniesModule),
    ConfigModule,
    FilesModule,
    ShortUrlsModule,
    LogsModule,
    CqrsModule,
    forwardRef(() => CustomersModule),
    GupshupBillingEventsModule,
    AiAgentsModule,
    ProductCatalogModule,
  ],
  exports: [
    GupshupService,
    GupshupWebhookHandlerService,
    GupshupPartnerService,
  ],
})
export class GupshupModule {}
