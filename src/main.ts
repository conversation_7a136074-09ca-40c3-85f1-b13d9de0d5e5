if (process.env.NODE_ENV === 'production') {
  import('newrelic');
}
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as Sentry from '@sentry/node';
import { AppModule } from './app.module';
import { PrismaExceptionFilter } from './shared/filters/prisma-exception.filter';
import { AllExceptionFilter } from './shared/filters/all-exception.filter';
import { PostgresIoAdapter } from './shared/adapters/postgres-io.adapter';
import { json, urlencoded } from 'express';
import { RABBIT_MQ_EMAILS_QUEUE_PREFETCH_COUNT } from './shared/constants/rabbitmq-emails-queue-prefetch-count';
import { RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_PREFETCH_COUNT } from './shared/constants/rabbitmq-whatsapp-messages-queue-prefetch-count';
import { RABBIT_MQ_SMS_MESSAGES_QUEUE_PREFETCH_COUNT } from './shared/constants/rabbitmq-sms-messages-queue-prefetch-count';
import { CommandBus } from '@nestjs/cqrs';
import { ApiTrackingInterceptor } from './shared/interceptors/api-tracking-request.interceptor';
import { LogsService } from './logs/logs.service';
import { RABBIT_MQ_COUPON_EVENTS_QUEUE_PREFETCH_COUNT } from './shared/constants/rabbitmq-coupon-events-queue-prefetch-count';
import { RABBIT_MQ_PRODUCT_EVENTS_QUEUE_PREFETCH_COUNT } from './shared/constants/rabbitmq-product-events-queue-prefetch-count';

async function bootstrap() {
  console.log('🚀 Starting application bootstrap...');
  const app = await NestFactory.create(AppModule);
  console.log('✅ NestFactory.create completed');

  // INITIALIZE SENTRY IN APPLICATION
  if (['production', 'staging'].includes(process.env.NODE_ENV || '')) {
    console.log('🔧 Initializing Sentry...');
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
    });
    console.log('✅ Sentry initialized');
  }

  app.useGlobalInterceptors(
    new ApiTrackingInterceptor(
      app.get(Reflector),
      app.get(CommandBus),
      app.get(LogsService),
    ),
  );

  // Socket.io adapter for multiple servers
  const postgressIoAdapter = new PostgresIoAdapter(app);
  await postgressIoAdapter.connectToPostgres();

  app.useWebSocketAdapter(postgressIoAdapter);
  app.useGlobalPipes(new ValidationPipe({ transform: true }));

  // AllExceptionFilter should be the first filter
  // it catches all exceptions if they are not caught by other filters
  app.useGlobalFilters(new AllExceptionFilter());
  app.useGlobalFilters(new PrismaExceptionFilter());

  app.use(json({ limit: '1mb' }));
  app.use(urlencoded({ extended: true, limit: '200kb' }));

  app.enableCors({
    origin: '*',
  });

  app.enableVersioning({
    type: VersioningType.URI,
  });

  app.enableShutdownHooks();

  for (const queue of [
    process.env.RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_1,
    process.env.RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_2,
    process.env.RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_3,
    process.env.RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_4,
    process.env.RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_5,
    process.env.RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_6,
  ]) {
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.RMQ,
      options: {
        noAck: false,
        prefetchCount: RABBIT_MQ_WHATSAPP_MESSAGES_QUEUE_PREFETCH_COUNT,
        urls: [
          encodeURI(
            `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
          ),
        ],
        queue,
        queueOptions: {
          durable: true,
        },
      },
    });
  }

  for (const queue of [
    process.env.RABBIT_MQ_SMS_MESSAGES_QUEUE_1,
    process.env.RABBIT_MQ_SMS_MESSAGES_QUEUE_2,
    process.env.RABBIT_MQ_SMS_MESSAGES_QUEUE_3,
  ]) {
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.RMQ,
      options: {
        noAck: false,
        prefetchCount: RABBIT_MQ_SMS_MESSAGES_QUEUE_PREFETCH_COUNT,
        urls: [
          encodeURI(
            `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
          ),
        ],
        queue,
        queueOptions: {
          durable: true,
        },
      },
    });
  }

  for (const queue of [
    process.env.RABBIT_MQ_EMAILS_QUEUE_1,
    process.env.RABBIT_MQ_EMAILS_QUEUE_2,
    process.env.RABBIT_MQ_EMAILS_QUEUE_3,
  ]) {
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.RMQ,
      options: {
        noAck: false,
        prefetchCount: RABBIT_MQ_EMAILS_QUEUE_PREFETCH_COUNT,
        urls: [
          encodeURI(
            `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
          ),
        ],
        queue,
        queueOptions: {
          durable: true,
        },
      },
    });
  }

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      noAck: false,
      prefetchCount: 10,
      urls: [
        encodeURI(
          `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
        ),
      ],
      queue: process.env.RABBIT_MQ_GUPSHUP_EVENTS_QUEUE,
      queueOptions: {
        durable: true,
      },
    },
  });
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      noAck: false,
      prefetchCount: Number(
        process.env.RABBIT_MQ_TIME_DELAY_EVENTS_QUEUE_PREFETCH_COUNT || 10,
      ),
      urls: [
        encodeURI(
          `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
        ),
      ],
      queue: process.env.RABBIT_MQ_TIME_DELAY_EVENTS_QUEUE,
      queueOptions: {
        durable: true,
      },
    },
  });
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      noAck: false,
      prefetchCount: Number(
        process.env.RABBIT_MQ_ORDER_EVENTS_QUEUE_PREFETCH_COUNT || 10,
      ),
      urls: [
        encodeURI(
          `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
        ),
      ],
      queue: process.env.RABBIT_MQ_ORDER_EVENTS_QUEUE,
      queueOptions: {
        durable: true,
      },
    },
  });
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      noAck: false,
      prefetchCount: RABBIT_MQ_COUPON_EVENTS_QUEUE_PREFETCH_COUNT,
      urls: [
        encodeURI(
          `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
        ),
      ],
      queue: process.env.RABBIT_MQ_COUPON_EVENTS_QUEUE,
      queueOptions: {
        durable: true,
      },
    },
  });
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      noAck: false,
      prefetchCount: RABBIT_MQ_PRODUCT_EVENTS_QUEUE_PREFETCH_COUNT,
      urls: [
        encodeURI(
          `amqp://${process.env.RABBIT_MQ_USER}:${process.env.RABBIT_MQ_PASSWORD}@${process.env.RABBIT_MQ_HOST}`,
        ),
      ],
      queue: process.env.RABBIT_MQ_PRODUCT_EVENTS_QUEUE,
      queueOptions: {
        durable: true,
      },
    },
  });
  await app.startAllMicroservices();
  await app.listen(process.env.PORT || 3000);
}
bootstrap();
