import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { Company, Prisma } from '@prisma/client';
import { CartsService } from 'src/carts/services/carts.service';
import { CompaniesService } from 'src/companies/companies.service';
import { CustomersService } from 'src/customers/customers.service';
import { LogsService } from 'src/logs/logs.service';
import { VtexGateway } from './gateways/vtex.gateway';
import { CartItemWithProductVariants } from 'src/carts/types/CartItem';
import {
  VtexOrderDto,
  VtexOrderPackageAttachment,
  VtexOrderShippingData,
} from './dto-temp/VtexOrder.dto';
import { ValueConverterUtils } from 'src/shared/utils/value-converter.utils';
import { Mapper, MapperPattern } from './utils-temp/mapper.utils';
import {
  MapperOrder,
  MapperOrderCustomer,
} from './types-temp/MapperOrderDefault.interface';

@Injectable()
export class VtexTempService {
  constructor(
    private readonly companiesService: CompaniesService,
    private readonly logsService: LogsService,
    private readonly customersService: CustomersService,
    private readonly cartsService: CartsService,
  ) {}

  private async getCompanies({
    where = {
      isVtexActive: true,
      vtexAccountName: {
        not: null,
      },
      vtexAppKey: {
        not: null,
      },
      vtexAppToken: {
        not: null,
      },
      vtexStoreDomain: {
        not: null,
      },
    },
  }: {
    where: Prisma.CompanyWhereInput;
  }): Promise<Company[]> {
    const companies = await this.companiesService.getCompaniesBy({
      where,
    });

    return companies;
  }
  private async getCompanyById(companyId: string): Promise<Company> {
    const companies = await this.getCompanies({
      where: { id: companyId, isVtexActive: true },
    });
    if (companies.length == 0) {
      throw new BadRequestException('Vtex is not active for this company');
    }

    return companies[0];
  }

  async finishCart({
    companyId,
    customerId,
  }: {
    companyId: string;
    customerId: string;
  }) {
    const company = await this.getCompanyById(companyId);
    if (!company) {
      throw new BadRequestException('Nenhuma empresa encontrada');
    }
    const customer = await this.customersService.findCustomer({
      id: customerId,
    });

    if (!customer) {
      throw new BadRequestException('Cliente não encontrado');
    }
    const cart = await this.cartsService.findCartByCustomerId({
      customerId,
      companyId,
    });
    if (!cart) {
      throw new BadRequestException(
        'Não foi encontrado nenhum carrinho ativo para este cliente',
      );
    }

    const cartItems = (await this.cartsService.listCartItems(
      {
        cartId: cart.id,
        quantity: {
          gt: 0,
        },
      },
      { productVariant: true },
    )) as CartItemWithProductVariants[];
    if (!cart) {
      throw new BadRequestException(
        'O carrinho informando não possui itens. É necessário adicionar itens antes de finalizar e gerar url do carrinho',
      );
    }

    const vtexGateway = new VtexGateway(
      company.vtexStoreDomain!,
      company.vtexAccountName!,
      company.vtexAppKey!,
      company.vtexAppToken!,
    );
    const createdCart = await vtexGateway.createEmptyCart();
    const orderItems = cartItems.map((value, index) => ({
      quantity: value.quantity,
      seller: createdCart['salesChannel'],
      id: value.productVariant.sourceId!,
      index,
    }));
    await vtexGateway.addItemToCart(createdCart['orderFormId'], {
      orderItems,
    });

    await this.cartsService.updateCart({
      where: {
        id: cart.id,
      },
      data: {
        status: 'finished',
      },
    });

    return {
      cart: 'createdCart',
      cart_url: `https://${company.vtexStoreDomain}/login?returnUrl=/checkout?orderFormId=${createdCart['orderFormId']}`,
    };
  }

  async fetchLastOrderByCustomerEmail({
    customerEmail,
    companyId,
  }: {
    customerEmail: string;
    companyId: string;
  }): Promise<{
    order: Omit<Prisma.OrderUncheckedCreateInput, 'customerId' | 'companyId'>;
    shippingData: VtexOrderShippingData;
    packageAttachment: VtexOrderPackageAttachment;
    customer: Omit<Prisma.CustomerUncheckedCreateInput, 'id' | 'companyId'>;
  }> {
    if (!customerEmail) {
      throw new BadRequestException('E-mail do cliente é obrigatório');
    }
    const company = await this.getCompanyById(companyId);
    const vtexGateway = new VtexGateway(
      company.vtexStoreDomain!,
      company.vtexAccountName!,
      company.vtexAppKey!,
      company.vtexAppToken!,
    );
    const orders = await vtexGateway.listOrdersByCustomerEmail(customerEmail);
    if (orders.length == 0) {
      throw new BadRequestException('Nenhum pedido encontrado');
    }
    orders.sort(
      (a, b) =>
        new Date(b.lastChange).getTime() - new Date(a.lastChange).getTime(),
    );
    const completeOrder = await vtexGateway.fetchOrder({
      orderId: orders[0].orderId,
    });

    const mappedOrder = this.getMappedOrder(completeOrder);
    const mappedCustomer = this.getMappedCustomer(completeOrder);

    return {
      order: mappedOrder,
      shippingData: completeOrder.shippingData,
      packageAttachment: completeOrder.packageAttachment,
      customer: mappedCustomer,
    };
  }

  private getMappedOrder(
    order: VtexOrderDto,
  ): Omit<Prisma.OrderUncheckedCreateInput, 'customerId' | 'companyId'> {
    const mapperOrder: MapperOrder = {
      sourceId: { path: 'orderId' },
      source: { path: 'origin' },
      sourceCreatedAt: { path: 'creationDate' },
      sourceUpdatedAt: { path: 'lastChange' },
      status: { path: 'status' },
      value: {
        path: 'value',
        transform: (value) => ValueConverterUtils.toNormalValue(value),
      },
      salesChannel: { path: 'salesChannel' },
      coupon: { path: 'marketingData.coupon' },
      currency: { path: 'storePreferencesData.currencyCode' },
      cancelReason: { path: 'cancelReason' },
      cancelledAt: { path: 'invoicedDate' },
      customerId: { path: 'clientProfileData.id' },
      companyId: { path: 'merchantName' },
      note: { path: 'statusDescription' },
      marketingData: { path: 'marketingData' },
      // paymentMethods: { path: 'paymentData.transactions' },
      totalItemsQuantity: {
        path: 'items',
        transform: (items: any[]) => items.length,
      },
      totalItemsValue: {
        path: 'totals',
        transform: (totals: any[]) => {
          const total = totals?.find((t: any) => t.id === 'Items');
          return total ? ValueConverterUtils.toNormalValue(total.value) : null;
        },
      },
      totalShippingValue: {
        path: 'totals',
        transform: (totals: any[]) => {
          const shipping = totals?.find((t: any) => t.id === 'Shipping');
          return shipping
            ? ValueConverterUtils.toNormalValue(shipping.value)
            : null;
        },
      },
      totalDiscountsValue: {
        path: 'totals',
        transform: (totals: any[]) => {
          const discounts = totals?.find((t: any) => t.id === 'Discounts');
          return discounts
            ? ValueConverterUtils.toNormalValue(discounts.value)
            : null;
        },
      },
      shippingCarrier: {
        path: 'shippingData.logisticsInfo[0].deliveryCompany',
      },
      trackingCode: {
        path: 'shippingData.logisticsInfo[0].trackingNumber',
      },
    };

    const mapper = new Mapper(mapperOrder as unknown as MapperPattern);
    return mapper.map<
      Omit<Prisma.OrderUncheckedCreateInput, 'customerId' | 'companyId'>
    >(order);
  }
  private getMappedCustomer(
    order: VtexOrderDto,
  ): Omit<Prisma.CustomerUncheckedCreateInput, 'id' | 'companyId'> {
    const mapperCustomer: MapperOrderCustomer = {
      'customer.sourceId': { path: 'clientProfileData.id' },
      'customer.sourceCustomerId': { path: 'clientProfileData.id' },
      'customer.name': {
        path: 'clientProfileData',
        transform: (profile: any) => {
          return `${profile.firstName || ''} ${profile.lastName || ''}`.trim();
        },
      },
      'customer.email': { path: 'clientProfileData.email' },
      'customer.phoneNumberId': { path: 'clientProfileData.phone' },
      'customer.source': { path: 'origin' },
      'customer.sourceCreatedAt': { path: 'creationDate' },
      'customer.sourceUpdatedAt': { path: 'lastChange' },
      'customer.city': { path: 'shippingData.address.city' },
      'customer.state': { path: 'shippingData.address.state' },
      'customer.country': { path: 'shippingData.address.country' },
      'customer.cpf': { path: 'clientProfileData.document' },
      'customer.companyId': { path: 'merchantName' },
      'customer.notes': { path: 'statusDescription' },
    };

    const mapper = new Mapper(mapperCustomer as unknown as MapperPattern);
    return mapper.map<
      Omit<Prisma.CustomerUncheckedCreateInput, 'id' | 'companyId'>
    >(order);
  }
}
