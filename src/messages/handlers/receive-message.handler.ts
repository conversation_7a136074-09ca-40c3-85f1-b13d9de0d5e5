import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import {
  ConversationTicket,
  Customer,
  Flow,
  FlowNodeType,
  FlowType,
  Message,
  MessageTemplateType,
  Prisma,
  SourceIntegration,
} from '@prisma/client';
import {
  add,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
} from 'date-fns';
import { AutomaticRepliesService } from 'src/automatic-replies/automatic-replies.service';
import { AutomaticSortingOptionsService } from 'src/automatic-sorting-options/automatic-sorting-options.service';
import { CompanyWithIncludes } from 'src/companies/types/CompanyWithIncludes';
import { CompanyDefinedFieldsService } from 'src/company-defined-fields/company-defined-fields.service';
import { ConversationTicketsService } from 'src/conversation-tickets/conversation-tickets.service';
import { ConversationsService } from 'src/conversations/conversations.service';
import { ConversationWithIncludes } from 'src/conversations/types/ConversationWithIncludes';
import { CustomersService } from 'src/customers/customers.service';
import { FlowEventsService } from 'src/flow-events/flow-events.service';
import { FlowNodeButtonsService } from 'src/flow-node-buttons/flow-node-buttons.service';
import { AddTagToCustomerNodeData } from 'src/flows/schemas/add-tag-to-customer-node-data.schema';
import { MoveToConversationCategoryNodeData } from 'src/flows/schemas/move-to-conversation-category-node-data.schema';
import { SaveCustomerResponseNodeData } from 'src/flows/schemas/save-customer-response-node-data.schema';
import { SendWhatsappMediaNodeData } from 'src/flows/schemas/send-whatsapp-media-node-data.schema';
import { SendWhatsappMessageNodeData } from 'src/flows/schemas/send-whatsapp-message-node-data.schema';
import { MessagesService } from 'src/messages/messages.service';
import {
  FlowNodeConditionBlockWithIncludes,
  TargetFlowNode,
} from 'src/shared/types/TargetFlowNode';
import { GupshupFileUtils } from 'src/shared/utils/gupshup-file.utils';
import { ConversationCategoriesService } from './../../conversation-categories/conversation-categories.service';
import { FlowTriggersService } from './../../flow-triggers/flow-triggers.service';
import { ReceiveMessageCommand } from './../commands/receive-message.command';

import { PrismaService } from 'src/prisma/prisma.service';
import { TagsService } from 'src/tags/tags.service';
import {
  ConditionType,
  FlowNodeConditionsQueryBuilder,
} from '../builder/flow-condition.query-builder';
import {
  AUTO_REPLY_COMMON_WORDS_REGEX,
  AUTO_REPLY_SENTENCES,
  SPECIAL_AUTO_REPLY_PATTERN_REGEX,
} from '../constants/auto-reply.constants';

import { ConfigService } from '@nestjs/config';
import { CompaniesService } from 'src/companies/companies.service';
import { SendWhatsappMessageTemplateNodeData } from 'src/flows/schemas/send_whatsapp_message_template-node-data.schema';
import { RabbitMQService } from 'src/rabbitmq/rabbitmq.service';
import {
  TemplateParametersEnum,
  TemplateParametersList,
} from 'src/shared/types/TemplateParametersEnum';
import {
  MessageTemplateUtils,
  TemplateParametersRegexp,
} from 'src/shared/utils/message-template.utils';
import { NameUtils } from 'src/shared/utils/name.utils';
import { BadRequestException, forwardRef, Inject } from '@nestjs/common';
import {
  FlowNodesService,
  FlowNodeWithIncludes,
} from './../../flow-nodes/flow-nodes.service';
import axios from 'axios';
import { HttpRequestNodeData } from 'src/flows/schemas/http-request-node-data.schema';
import { UserVariablesEnum } from 'src/shared/types/UserVariablesEnum';
import MapperUtils from 'src/shared/utils/mapper.utils';
import { TimeUtils } from 'src/shared/utils/time.utils';
import { MessageTemplatesService } from 'src/message-templates/message-templates.service';
import { EmailsService } from 'src/emails/emails.service';
import {
  RETRIABLE_FLOW_NODE_DUE_INVALID_POSTBACK_RESPONSES,
  RETRIABLE_FLOW_NODES_DUE_INVALID_POSTBACK,
} from '../constants/invalid-flow-postback.constants';
import { AssignConversationTicketToAgentNodeData } from 'src/flows/schemas/assign-conversation-ticket-to-agent.schema';
import { ArrayUtils } from 'src/shared/utils/array.utils';
import { FilesService } from 'src/files/files.service';
import { AiAgentsService } from 'src/ai-agents/ai-agents.service';
import { UsersService } from 'src/users/users.service';
import { DebounceUtils } from 'src/shared/utils/debounce.utils';
import {
  AUTOMATION_WHATSAPP_MESSAGE_LIMIT_ERROR,
  WHATSAPP_MESSAGE_LIMIT_ERROR,
} from '../constants/whatsapp-message-limit-errors';
import { AbandonedCartsService } from 'src/abandoned-carts/abandoned-carts.service';
import { CartsService } from 'src/carts/services/carts.service';
import { VtexTempService } from 'src/vtex/vtex.temp.service';

interface ReceiveMessageHandlingContext {
  receiveMessageCommand: ReceiveMessageCommand;
  createdMessage: Message;
  conversation: ConversationWithIncludes;
  currentOpenTicket: ConversationTicket | undefined;
  lastSentMessage: Message | undefined;
  isNewConversationTicket: boolean;
}

@CommandHandler(ReceiveMessageCommand)
export class ReceiveMessageHandler
  implements ICommandHandler<ReceiveMessageCommand>
{
  constructor(
    @Inject(forwardRef(() => ConversationsService))
    private readonly conversationsService: ConversationsService,
    private readonly conversationCategoriesService: ConversationCategoriesService,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    private readonly automaticRepliesService: AutomaticRepliesService,
    private readonly automaticSortingOptionsService: AutomaticSortingOptionsService,
    @Inject(forwardRef(() => ConversationTicketsService))
    private readonly conversationTicketService: ConversationTicketsService,
    private readonly flowTriggersService: FlowTriggersService,
    private readonly flowNodeButtonsService: FlowNodeButtonsService,
    private readonly flowNodesService: FlowNodesService,
    @Inject(forwardRef(() => CustomersService))
    private readonly customersService: CustomersService,
    private readonly tagsService: TagsService,
    private readonly flowEventsService: FlowEventsService,
    private readonly companyDefinedFieldsService: CompanyDefinedFieldsService,
    @Inject(forwardRef(() => CompaniesService))
    private readonly companiesService: CompaniesService,
    private readonly prismaService: PrismaService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly configService: ConfigService,
    private readonly messageTemplatesService: MessageTemplatesService,
    @Inject(forwardRef(() => EmailsService))
    private readonly emailsService: EmailsService,
    private readonly filesService: FilesService,
    private readonly aiAgentsService: AiAgentsService,
    private readonly usersService: UsersService,
    private readonly abandonedCartsService: AbandonedCartsService,
    private readonly cartsService: CartsService,
    private readonly vtexTempService: VtexTempService,
  ) {}

  async execute(receiveMessageCommand: ReceiveMessageCommand) {
    const createdMessage = await this.saveMessage(receiveMessageCommand);

    const context = await this.prepareReceiveMessageHandlingContext({
      receiveMessageCommand,
      createdMessage,
    });

    await this.saveFirstReply(context);
    if (await this.processWhatsappOrder(context)) return createdMessage;
    if (await this.sendAiAgentMessage(context)) return createdMessage;
    if (await this.optOutCustomer(context)) return createdMessage;
    if (await this.optInCustomer(context)) return createdMessage;

    if (await this.sendAutoReplyMessage(context)) return createdMessage;

    if (await this.handleFlowNodeAction(context)) return createdMessage;
    if (await this.sendFlowTriggerMessage(context)) return createdMessage;

    if (await this.sendAfterHoursMessage(context)) return createdMessage;
    if (await this.sendFirstContactMessage(context)) return createdMessage;

    return createdMessage;
  }

  private async processWhatsappOrder(context: ReceiveMessageHandlingContext) {
    const { receiveMessageCommand } = context;
    if (!receiveMessageCommand.payload?.order) return false;
    const { createdMessage } = context;
    const order = receiveMessageCommand.payload.order;
    try {
      await this.cartsService.createCart({
        companyId: context.receiveMessageCommand.company.id,
        customerId: context.conversation.customerId,
        source: SourceIntegration.vtex_ecommerce,
        status: 'active',
        cartItem: {
          create: order.catalog.order.items.map((item) => ({
            productVariantId: item.id,
            quantity: Number(item.quantity),
            price: Number(item.amount),
          })),
        },
      });
      const vtexCartResponse = await this.vtexTempService.finishCart({
        companyId: context.receiveMessageCommand.company.id,
        customerId: context.conversation.customerId,
      });
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: context.receiveMessageCommand.company.id,
        senderPhoneNumberId: context.receiveMessageCommand.senderPhoneNumberId,
        conversationId: context.conversation.id,
        text: `Seu carrinho foi gerado com sucesso. Acesse o link para finalizar o pagamento.`,
        buttons: [
          {
            id: createdMessage.id,
            title: 'Pagar agora',
            url: vtexCartResponse.cart_url,
          },
        ],
      });
    } catch (error) {
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: context.receiveMessageCommand.company.id,
        senderPhoneNumberId: context.receiveMessageCommand.senderPhoneNumberId,
        conversationId: context.conversation.id,
        text: 'Carrinho recebido com sucesso. Vamos transferir seu atendimento para um atendente para gerar o link de pagamento.',
      });

      const currentOpenTicket = context.conversation.conversationTickets.at(0);
      if (!currentOpenTicket) return false;
      await this.conversationTicketService.updateConversationTicket({
        companyId: context.receiveMessageCommand.company.id,
        where: {
          id: currentOpenTicket.id,
        },
        data: {
          agentId: null,
        },
      });

      return true;
    }

    return true;
  }

  private async sendAiAgentMessage(context: ReceiveMessageHandlingContext) {
    const currentOpenTicket = context.conversation.conversationTickets.at(0);
    if (!currentOpenTicket?.agentId) return false;

    const agent = await this.usersService.findUser({
      id: currentOpenTicket.agentId,
      companyId: context.receiveMessageCommand.company.id,
      isAiAgent: true,
    });
    if (!agent) return false;

    const onAgentError = async (error: any) => {
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: context.receiveMessageCommand.company.id,
        senderPhoneNumberId:
          context.receiveMessageCommand.company.phoneNumberId,
        conversationId: context.conversation.id,
        text: `Quero muito te ajudar, mas encontrei um probleminha técnico aqui 😓
Vou te passar agora para um dos nossos atendentes — eles vão continuar o atendimento e garantir que tudo saia certinho. Só um instante!`,
      });
      await this.conversationTicketService.updateConversationTicket(
        {
          where: { id: currentOpenTicket.id },
          companyId: context.conversation.companyId,
          data: {
            agentId: null,
          },
          notifyUser: true,
        },
        {
          skipAssignDefaultAgent: true,
        },
      );
    };

    const DEBOUNCE_DELAY_TO_PROCESS_MESSAGE_BY_AI = 5000;
    if (agent.id === 'orlando') {
      DebounceUtils.debounceByKeyAsync(
        context.conversation.id,
        async (count: number) =>
          await this.aiAgentsService.processCustomerMessage({
            conversation: context.conversation,
            company: context.receiveMessageCommand.company,
            qtyMessagesSent: count,
          }),
        DEBOUNCE_DELAY_TO_PROCESS_MESSAGE_BY_AI,
        onAgentError,
      );
    } else {
      DebounceUtils.debounceAiAgentMessageByConversationId(
        context.conversation.id,
        context.createdMessage.text,
        async (count: number, finalMessage: string) =>
          await this.aiAgentsService.processCustomerMessage2({
            conversation: context.conversation,
            company: context.receiveMessageCommand.company,
            message: finalMessage,
          }),
        DEBOUNCE_DELAY_TO_PROCESS_MESSAGE_BY_AI,
        onAgentError,
      );
    }

    return true;
  }

  private async optInCustomer(context: ReceiveMessageHandlingContext) {
    const {
      receiveMessageCommand: { text, company },
      conversation,
    } = context;
    if (text === 'VOLTAR') {
      await this.customersService.updateCustomer({
        where: {
          id: context.conversation.customerId,
        },
        data: {
          isOptedOut: false,
        },
      });

      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: conversation.companyId,
        senderPhoneNumberId: company.phoneNumberId,
        conversationId: conversation.id,
        text: 'Você foi adicionado de volta à nossa lista de contatos',
      });

      return true;
    }
    return false;
  }
  private async optOutCustomer(context: ReceiveMessageHandlingContext) {
    const {
      receiveMessageCommand: { text, company },
      conversation,
    } = context;

    const companyHasCustomOptoutMessage = company.optoutMessage !== null;
    const optoutMessage = companyHasCustomOptoutMessage
      ? company.optoutMessage
      : 'stop';

    if (text?.toLowerCase() === optoutMessage?.toLowerCase()) {
      await this.customersService.updateCustomer({
        where: {
          id: context.conversation.customerId,
        },
        data: {
          isOptedOut: true,
        },
      });

      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: conversation.companyId,
        senderPhoneNumberId: company.phoneNumberId,
        conversationId: conversation.id,
        text: 'Você foi removido da nossa lista de contatos e deixará de receber promoções e novidades personalizadas. Se você quiser voltar, envie VOLTAR',
      });

      return true;
    }
    return false;
  }

  private async sendFlowTriggerMessage(context: ReceiveMessageHandlingContext) {
    const { receiveMessageCommand, conversation, createdMessage } = context;
    if (!createdMessage.text) return false;
    const flowTrigger =
      await this.flowTriggersService.matchMessageWithFlowTrigger(
        receiveMessageCommand.company.id,
        createdMessage.text,
        receiveMessageCommand?.payload,
      );
    if (!flowTrigger) return false;

    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: flowTrigger.targetFlowNodeId,
      },
    });

    await this.createFlowEvent({
      conversation,
      createdMessage,
      flowId: flowTrigger.flowId,
      flowTriggerId: flowTrigger.id,
      flowNodeData: {
        type: flowTrigger.type,
        text: flowTrigger.text,
        isDefault: flowTrigger.isDefault,
      },
    });

    return await this.executeFlowNodeAction(
      context.conversation,
      flowTrigger.targetFlowNodeId,
    );
  }

  private async getFlowNodeData(flowNodeId: string) {
    return await this.flowNodesService.findFlowNode({ id: flowNodeId });
  }

  private async getTextWithParametersFromPreviousHttpRequestResponse(params: {
    text: string;
    nodeId: string;
    flowId: string;
    conversationId: string;
  }): Promise<string> {
    if (!params.text.match(TemplateParametersRegexp)) {
      return params.text;
    }

    const httpRequests =
      await this.flowEventsService.listPreviousFlowEventsFromFlowNodeId({
        nodeId: params.nodeId,
        flowId: params.flowId,
        conversationId: params.conversationId,
        flowNodeType: FlowNodeType.http_request,
      });

    if (httpRequests && httpRequests.length > 0) {
      let messageWithParams = params.text;
      const parametersInText =
        MessageTemplateUtils.getAllParametersInText(messageWithParams);
      httpRequests.forEach((httpRequest) => {
        const response = httpRequest.metadata?.['mappedResponse'] ?? null;

        if (!response) return;

        parametersInText.forEach((parameter) => {
          if (TemplateParametersList.includes(parameter)) return;

          const cleanParam = parameter.replace('[', '').replace(']', '');

          const value = response[cleanParam];
          if (value === undefined || value === null) return;

          const regex = new RegExp(
            parameter.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
            'g',
          );

          messageWithParams = messageWithParams.replace(regex, value);
        });
      });
      return messageWithParams;
    }

    return params.text;
  }

  private async createFlowEvent({
    conversation,
    createdMessage,
    flowNodeId,
    flowNodeButtonId,
    flowNodeData,
    flowTriggerId,
    flowId,
  }: {
    conversation: ConversationWithIncludes;
    createdMessage?: Message;
    flowId: string;
    flowNodeId?: string;
    flowTriggerId?: string;
    flowNodeButtonId?: string;
    flowNodeData?: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue;
  }) {
    let nodeData = flowNodeData;
    const currentFlowNodeId = flowNodeId ?? conversation.currentFlowNodeId;

    if (!nodeData && conversation.currentFlowNodeId) {
      const flowNode = await this.getFlowNodeData(
        conversation.currentFlowNodeId,
      );
      nodeData = flowNode
        ? (flowNode.data as Prisma.InputJsonValue)
        : Prisma.JsonNull;
    }

    await this.flowEventsService.createFlowEvent({
      companyId: conversation.companyId,
      conversationId: conversation.id,
      messageId: createdMessage?.id ?? null,
      flowId: flowId,
      flowNodeId: currentFlowNodeId,
      flowNodeButtonId,
      metadata: nodeData,
      flowTriggerId,
      fromSystem: createdMessage?.fromSystem ?? false,
    });
  }

  private async handleFlowNodeAction(context: ReceiveMessageHandlingContext) {
    const {
      receiveMessageCommand: { postbackText, company },
      conversation,
    } = context;
    if (!conversation.currentFlowNodeId) return false;

    const flowNode = await this.getFlowNodeData(conversation.currentFlowNodeId);
    if (!flowNode) return false;

    if (
      await this.shouldRetryFlowNode(postbackText, flowNode.type, flowNode.flow)
    ) {
      return await this.retryFlowNodeAction(conversation, flowNode, company);
    }

    if (
      await this.shouldRemoveConversationFromFlow(postbackText, flowNode.type)
    ) {
      await this.removeConversationFromFlow(conversation);
      return false;
    }

    if (
      await this.isFlowNodeDependentOnUserAction(postbackText, flowNode.type)
    ) {
      return await this.executeFlowNodeDependentOnUserAction(context, flowNode);
    }

    if (flowNode.nextFlowNodeId) {
      return await this.executeFlowNodeAction(
        context.conversation,
        flowNode.nextFlowNodeId,
      );
    }

    return true;
  }

  private async shouldRemoveConversationFromFlow(
    postbackText: string | undefined,
    flowNodeType: FlowNodeType,
  ) {
    return (
      !postbackText && flowNodeType !== FlowNodeType.save_customer_response
    );
  }

  private async removeConversationFromFlow(
    conversation: ConversationWithIncludes,
  ) {
    await this.conversationsService.removeConversationFromFlow(conversation.id);
  }

  private async shouldRetryFlowNode(
    postbackText: string | undefined,
    flowNodeType: FlowNodeType,
    flow: Flow,
  ) {
    return (
      flow.repeatOnInvalidInput &&
      !postbackText &&
      RETRIABLE_FLOW_NODES_DUE_INVALID_POSTBACK.includes(flowNodeType)
    );
  }

  async retryFlowNodeAction(
    conversation: ConversationWithIncludes,
    flowNode: FlowNodeWithIncludes,
    company: CompanyWithIncludes,
  ) {
    const text =
      RETRIABLE_FLOW_NODE_DUE_INVALID_POSTBACK_RESPONSES?.[flowNode.type] ||
      RETRIABLE_FLOW_NODE_DUE_INVALID_POSTBACK_RESPONSES.default;

    await this.messagesService.sendMessage({
      isAutomaticResponse: true,
      companyId: conversation.companyId,
      senderPhoneNumberId: company.phoneNumberId,
      conversationId: conversation.id,
      text,
    });

    return await this.executeFlowNodeAction(conversation, flowNode.id);
  }

  private async isFlowNodeDependentOnUserAction(
    postbackText: string | undefined,
    currentFlowNodeType: FlowNodeType,
  ) {
    return (
      Boolean(postbackText?.trim()) ||
      currentFlowNodeType === FlowNodeType.save_customer_response
    );
  }

  private async executeFlowNodeDependentOnUserAction(
    context: ReceiveMessageHandlingContext,
    currentFlowNode: FlowNodeWithIncludes,
  ) {
    const {
      receiveMessageCommand: { postbackText },
      conversation,
    } = context;

    if (postbackText) {
      return await this.processPostbackText(postbackText, context);
    } else if (currentFlowNode.type === FlowNodeType.save_customer_response) {
      await this.executeSaveCustomerResponse(context, currentFlowNode);
    }

    if (currentFlowNode.nextFlowNodeId) {
      return await this.executeFlowNodeAction(
        context.conversation,
        currentFlowNode.nextFlowNodeId,
      );
    }

    return true;
  }

  async executeFlowNodeAction(
    conversation: ConversationWithIncludes,
    targetFlowNodeId: string,
  ) {
    const targetFlowNode = await this.flowNodesService.findFlowNode(
      {
        id: targetFlowNodeId,
      },
      { isDeleted: false },
    );
    if (!targetFlowNode) return false;

    const delayBetweenMessages = 2000;
    if (targetFlowNode.type === 'save_customer_response') {
      return await this.prepareForSaveCustomerResponse(
        conversation,
        targetFlowNode,
      );
    } else if (targetFlowNode.type === 'send_whatsapp_message') {
      await this.executeSendWhatsappMessage(conversation, targetFlowNode);
      await TimeUtils.wait(delayBetweenMessages);
    } else if (targetFlowNode.type === 'move_conversation_to_category') {
      await this.executeMoveConversationToCategory(
        conversation,
        targetFlowNode,
      );
    } else if (targetFlowNode.type === 'send_whatsapp_media') {
      await this.executeSendWhatsappMedia(conversation, targetFlowNode);
      await TimeUtils.wait(delayBetweenMessages);
    } else if (targetFlowNode.type === 'add_tag_to_customer') {
      await this.executeAddTagToCustomer(conversation, targetFlowNode);
    } else if (targetFlowNode.type === 'conditions_check') {
      await this.executeConditionsCheck(conversation, targetFlowNode);
    } else if (targetFlowNode.type === 'time_delay') {
      return await this.executeTimeDelay(conversation, targetFlowNode);
    } else if (targetFlowNode.type === 'send_whatsapp_message_template') {
      await this.executeSendWhatsappMessageTemplate(
        conversation,
        targetFlowNode,
      );
    } else if (
      targetFlowNode.type === ('send_email_template' as FlowNodeType)
    ) {
      await this.executeSendEmailTemplate(conversation, targetFlowNode);
    } else if (targetFlowNode.type === 'end_whatsapp_conversation') {
      await this.executeEndWhatsappConversation(conversation);
    } else if (targetFlowNode.type === 'http_request') {
      const success = await this.executeHttpRequest(
        conversation,
        targetFlowNode,
      );
      if (!success) {
        targetFlowNode.nextFlowNodeId = targetFlowNode.nextFlowNodeOnErrorId;
      }
    } else if (targetFlowNode.type === 'assign_conversation_ticket_to_agent') {
      await this.executeAssignConversationTicketToAgent(
        conversation,
        targetFlowNode,
      );
    }
    if (targetFlowNode.nextFlowNodeId) {
      return await this.executeFlowNodeAction(
        conversation,
        targetFlowNode.nextFlowNodeId,
      );
    }
    return true;
  }

  private async processPostbackText(
    postbackText: string,
    context: ReceiveMessageHandlingContext,
  ) {
    const currentNodeButton =
      await this.flowNodeButtonsService.findAndIncrementFlowNodeButtonId(
        postbackText,
      );
    if (!currentNodeButton || !currentNodeButton.targetFlowNodeId) return false;
    const flowNode = await this.getFlowNodeData(currentNodeButton.flowNodeId);
    if (!flowNode) return false;
    const { conversation, createdMessage } = context;

    await this.createFlowEvent({
      conversation,
      createdMessage,
      flowId: flowNode?.flowId ?? '',
      flowNodeId: currentNodeButton.flowNodeId,
      flowNodeButtonId: currentNodeButton.id,
      flowNodeData: flowNode
        ? (flowNode.data as Prisma.InputJsonValue)
        : Prisma.JsonNull,
    });

    const targetFlowNode = await this.flowNodesService.findFlowNode({
      id: currentNodeButton.targetFlowNodeId,
    });
    if (!targetFlowNode) return false;

    if (targetFlowNode.type === FlowNodeType.save_customer_response) {
      await this.executeSaveCustomerResponse(context, targetFlowNode);

      if (targetFlowNode.nextFlowNodeId) {
        return await this.executeFlowNodeAction(
          context.conversation,
          targetFlowNode.nextFlowNodeId,
        );
      }

      return true;
    }

    return await this.executeFlowNodeAction(
      context.conversation,
      currentNodeButton.targetFlowNodeId,
    );
  }

  private async executeSaveCustomerResponse(
    context: ReceiveMessageHandlingContext,
    flowNode: FlowNodeWithIncludes,
  ) {
    const { conversation } = context;
    const nodeData = flowNode.data as SaveCustomerResponseNodeData;
    if (!nodeData.companyDefinedFieldId) return false;

    const companyDefinedField =
      await this.companyDefinedFieldsService.findCompanyDefinedField({
        id: nodeData.companyDefinedFieldId,
      });
    if (!companyDefinedField) return false;

    const customer = await this.customersService.findCustomer({
      id: conversation.customerId,
    });
    if (!customer) return false;

    const existingCompanyDefinedFields = customer.customFields
      ? typeof customer.customFields === 'string'
        ? JSON.parse(customer.customFields)
        : customer.customFields
      : {};

    const updatedCompanyDefinedFields = {
      ...existingCompanyDefinedFields,
      [companyDefinedField.name]: context.createdMessage.text,
    };

    await this.createFlowEvent({
      conversation,
      flowId: flowNode.flowId,
      flowNodeId: flowNode.id,
      flowNodeData: { text: context.createdMessage.text },
      createdMessage: context.createdMessage,
    });

    await this.customersService.updateCustomer({
      where: {
        id: conversation.customerId,
      },
      data: {
        customFields: updatedCompanyDefinedFields,
      },
    });

    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: flowNode.nextFlowNodeId,
      },
    });
  }

  private async prepareForSaveCustomerResponse(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: targetFlowNode.id,
      },
    });
    return true;
  }

  private async executeMoveConversationToCategory(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const nodeData = targetFlowNode.data as MoveToConversationCategoryNodeData;
    if (!nodeData.targetConversationCategoryId) return false;

    const conversationCategory =
      await this.conversationCategoriesService.findConversationCategory({
        id: nodeData.targetConversationCategoryId,
      });

    if (!conversationCategory?.isDeleted) {
      await this.conversationsService.updateConversation({
        where: {
          id: conversation.id,
        },
        data: {
          categoryId: conversationCategory?.id || null,
          currentFlowNodeId: targetFlowNode.id,
        },
      });
    }

    return true;
  }

  private async executeSendWhatsappMedia(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const company = await this.companiesService.findCompany({
      id: conversation.companyId,
    });

    if (!company) {
      throw new Error('Empresa não encontrada');
    }

    const nodeData =
      targetFlowNode.data as unknown as SendWhatsappMediaNodeData;

    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: targetFlowNode.id,
      },
    });

    const message = await this.messagesService.sendMessage({
      isAutomaticResponse: true,
      companyId: company.id,
      senderPhoneNumberId: company.phoneNumberId,
      text: nodeData.fileName || '',
      conversationId: conversation.id,
      fileKey: nodeData.fileKey,
      mediaType: nodeData.mediaType,
      flowNodeId: targetFlowNode.id,
    });

    await this.createFlowEvent({
      flowId: targetFlowNode.flowId,
      flowNodeData: targetFlowNode.data as Prisma.InputJsonValue,
      conversation: conversation,
      createdMessage: message,
    });

    await new Promise((resolve) => setTimeout(resolve, 3000));

    return true;
  }

  private async replaceParametersFromCustomer(
    text: string,
    customerId: string,
  ) {
    if (!text.match(TemplateParametersRegexp)) {
      return text;
    }

    let resultText = text;

    const customer = await this.customersService.findCustomer({
      id: customerId,
    });

    if (!customer) {
      throw new BadRequestException('Cliente não encontrado');
    }

    const customerCustomDefinedFields = customer?.customFields
      ? typeof customer.customFields === 'string'
        ? JSON.parse(customer.customFields)
        : customer.customFields
      : undefined;

    const parametersInText = MessageTemplateUtils.getAllParametersInText(text);

    parametersInText.forEach((parameter) => {
      if (parameter === TemplateParametersEnum.CUSTOMER_NAME) {
        resultText = resultText.replace(
          parameter,
          NameUtils.getFirstName(customer.name)!,
        );
      }

      const fieldName = parameter.replace('[', '').replace(']', '');
      const value = customerCustomDefinedFields?.[fieldName];

      if (!value) return;
      resultText = resultText.replaceAll(parameter, value);
    });
    return resultText;
  }

  private async executeSendWhatsappMessage(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const company = await this.companiesService.findCompany({
      id: conversation.companyId,
    });

    if (!company) {
      throw new Error('Empresa não encontrada');
    }
    const nodeData =
      targetFlowNode.data as unknown as SendWhatsappMessageNodeData;

    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: targetFlowNode.id,
      },
    });

    let text = nodeData.text;
    // UNCOMMENT THIS TO ENABLE FLOW EXECUTION PARAMETERS FROM HTTP RESPONSE
    // text = await this.getTextWithParametersFromPreviousHttpRequestResponse({
    //   text: nodeData.text,
    //   nodeId: targetFlowNode.id,
    //   flowId: targetFlowNode.flowId,
    //   conversationId: conversation.id,
    // });
    text = await this.replaceParametersFromCustomer(
      text,
      conversation.customerId,
    );

    const buttons = targetFlowNode.flowNodeButtons.map((button) => ({
      title: button.text,
      id: button.id,
      url: button.url || undefined,
    }));

    const message = await this.messagesService.sendMessage({
      isAutomaticResponse: true,
      companyId: company.id,
      senderPhoneNumberId: company.phoneNumberId,
      conversationId: conversation.id,
      text,
      buttons,
      flowNodeId: targetFlowNode.id,
    });
    await this.createFlowEvent({
      flowId: targetFlowNode.flowId,
      flowNodeData: targetFlowNode.data as Prisma.InputJsonValue,
      conversation: conversation,
      createdMessage: message,
    });
    return true;
  }
  private async executeAddTagToCustomer(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const nodeData = targetFlowNode.data as AddTagToCustomerNodeData;
    if (!nodeData.tagId) return false;

    const tag = await this.tagsService.findTag({
      id: nodeData.tagId,
    });

    if (!tag) return false;

    const customerTag = await this.customersService.findCustomerTag({
      customerId: conversation.customerId,
      tagId: nodeData.tagId,
    });

    if (customerTag) return true;

    await this.customersService.addTagToCustomer(
      conversation.customerId,
      nodeData.tagId,
    );

    return true;
  }

  private async executeConditionsCheck(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    if (targetFlowNode.flowNodeConditionBlocks.length === 0) {
      return false;
    }

    // sorts by priority and make sure that the default condition is the last one
    const sortedConditionBlocks = targetFlowNode.flowNodeConditionBlocks.sort(
      (a, b) => {
        if (
          a.flowNodeConditions.some((c) => c.type === ConditionType.DEFAULT)
        ) {
          return 1;
        }
        if (
          b.flowNodeConditions.some((c) => c.type === ConditionType.DEFAULT)
        ) {
          return -1;
        }
        return a.priority - b.priority;
      },
    );

    for (const conditionBlock of sortedConditionBlocks) {
      const response = await this.checkConditionsForCustomer(
        conditionBlock,
        conversation.customerId,
      );

      if (response && conditionBlock.targetFlowNodeId) {
        await this.executeFlowNodeAction(
          conversation,
          conditionBlock.targetFlowNodeId,
        );
        break;
      }
    }

    return true;
  }

  async checkConditionsForCustomer(
    flowNodeConditionBlock: FlowNodeConditionBlockWithIncludes,
    customerId: string,
  ): Promise<boolean> {
    if (
      flowNodeConditionBlock.flowNodeConditions.some(
        (c) => c.type === ConditionType.DEFAULT,
      )
    )
      return true;

    const flowNodeConditionBuilder = new FlowNodeConditionsQueryBuilder(
      customerId,
    );
    const query = flowNodeConditionBuilder.build(flowNodeConditionBlock);

    const customers = await this.prismaService.$queryRaw<Customer[]>(query);

    return customers.length > 0;
  }

  private async executeTimeDelay(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: targetFlowNode.id,
      },
    });

    let executionTime = new Date();

    if (
      targetFlowNode.data &&
      targetFlowNode.data['unit'] &&
      targetFlowNode.data['quantity']
    ) {
      executionTime = add(new Date(), {
        [targetFlowNode.data['unit']]: targetFlowNode.data['quantity'],
      });
    }

    return await this.prismaService.flowNodeScheduledAction.create({
      data: {
        conversationId: conversation.id,
        flowNodeId: targetFlowNode.id,
        executionTime: executionTime,
      },
    });
  }

  private async executeSendWhatsappMessageTemplate(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const nodeData = targetFlowNode.data as SendWhatsappMessageTemplateNodeData;
    if (!nodeData.messageTemplateId) return false;

    const company = await this.companiesService.findCompany({
      id: conversation.companyId,
    });

    if (!company) {
      throw new Error('Empresa não encontrada');
    }

    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: targetFlowNode.id,
      },
    });

    const customer = await this.customersService.findCustomer({
      id: conversation.customerId,
    });

    if (!customer) {
      throw new Error('Cliente não encontrado');
    }

    if (!customer.phoneNumberId) {
      throw new Error('Cliente não possui número de telefone');
    }

    const mappedTemplateArgs = MessageTemplateUtils.mapTemplateArguments(
      nodeData.templateArgs || {},
      {
        ...customer,
        phoneNumberId: customer.phoneNumberId,
      },
    );

    const flowEvent = await this.flowEventsService.listFlowEvents({
      where: {
        companyId: conversation.companyId,
        flowId: targetFlowNode.flowId,
        conversationId: conversation.id,
        metadata: {
          path: ['automationId'],
          not: Prisma.JsonNull,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 1,
    });

    const automationId = flowEvent?.[0]?.metadata?.['automationId'] ?? null;

    const template = await this.messageTemplatesService.findMessageTemplate({
      id: nodeData.messageTemplateId,
    });
    const isAbandonedCart =
      template?.type === MessageTemplateType.ABANDONED_CART;
    const abandonedCartCTAandProducts =
      await this.prismaService.abandonedCart.findFirst({
        where: { phoneNumberId: customer.phoneNumberId },
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          cartUrl: true,
          products: true,
        },
      });

    try {
      const messageWithTemplate =
        await this.messagesService.sendMessageTemplate(
          {
            templateId: nodeData.messageTemplateId,
            templateArgs: {
              [TemplateParametersEnum.CUSTOMER_NAME]: NameUtils.getFirstName(
                customer.name,
              ),
              [TemplateParametersEnum.COMPANY_NAME]: company.name,
              ...mappedTemplateArgs,
              ...(isAbandonedCart && abandonedCartCTAandProducts
                ? {
                    [TemplateParametersEnum.CTA_LINK]:
                      abandonedCartCTAandProducts.cartUrl ?? undefined,
                    [TemplateParametersEnum.ABANDONED_CART_PRODUCTS]:
                      Array.isArray(abandonedCartCTAandProducts.products)
                        ? abandonedCartCTAandProducts.products
                            .map((p: any) => p.name)
                            .join(', ')
                        : '.',
                  }
                : {}),
            },
            conversationId: conversation.id,
            companyId: company.id,
            senderPhoneNumberId: company.phoneNumberId,
            automationId,
            flowNodeId: targetFlowNode.id,
          },
          false,
        );

      await this.createFlowEvent({
        flowId: targetFlowNode.flowId,
        flowNodeData: targetFlowNode.data as Prisma.InputJsonValue,
        conversation: conversation,
        createdMessage: messageWithTemplate,
      });

      return messageWithTemplate;
    } catch (error: any) {
      if (
        automationId &&
        abandonedCartCTAandProducts &&
        template?.type === MessageTemplateType.ABANDONED_CART
      ) {
        const errorMessage =
          error?.message === AUTOMATION_WHATSAPP_MESSAGE_LIMIT_ERROR ||
          error?.message === WHATSAPP_MESSAGE_LIMIT_ERROR
            ? error.message
            : 'Erro ao enviar carrinho abandonado';
        await this.abandonedCartsService.updateAbandonedCart({
          where: {
            id: abandonedCartCTAandProducts.id,
          },
          data: {
            status: 'failed',
            errorMessage: errorMessage,
          },
        });
      }

      throw new BadRequestException(
        'Erro ao enviar mensagem com template. Verifique os parâmetros e tente novamente.',
      );
    }
  }

  private async executeSendEmailTemplate(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const nodeData = targetFlowNode.data as {
      emailTemplateId: string;
      templateArgs?: Record<string, string | undefined>;
    };
    if (!nodeData.emailTemplateId) return false;

    await this.conversationsService.updateConversation({
      where: {
        id: conversation.id,
      },
      data: {
        currentFlowNodeId: targetFlowNode.id,
      },
    });

    const customer = await this.customersService.findCustomer({
      id: conversation.customerId,
    });

    if (!customer?.email) {
      return false;
    }

    await this.emailsService.sendEmailTemplateTest(
      {
        emailTemplateId: nodeData.emailTemplateId,
        recipientName: customer.name,
        recipientEmail: customer.email,
        templateArgs: nodeData.templateArgs || {},
      },
      conversation.companyId,
    );

    await this.createFlowEvent({
      flowId: targetFlowNode.flowId,
      flowNodeData: targetFlowNode.data as Prisma.InputJsonValue,
      conversation: conversation,
    });

    return true;
  }

  private async executeEndWhatsappConversation(
    conversation: ConversationWithIncludes,
  ) {
    await this.conversationsService.closeAllTickets(conversation.id);
    return true;
  }

  private async getCustomValueFromEnum(
    value: string,
    conversation: ConversationWithIncludes,
  ): Promise<string> {
    switch (value) {
      case UserVariablesEnum.CUSTOMER_NAME:
        return conversation.customer.name || '';
      case UserVariablesEnum.CUSTOMER_EMAIL:
        return conversation.customer.email || '';
      case UserVariablesEnum.CUSTOMER_ID:
        return conversation.customer.id || '';
      default:
        return '';
    }
  }

  private async mapAndSaveCustomerCustomFieldsFromHttpResult(
    data: any,
    customerId: string,
    mappings: { source: string; destination: string }[],
  ) {
    const newCustomerCustomFields = {};
    for (const map of mappings) {
      const companyDefinedField =
        await this.companyDefinedFieldsService.findCompanyDefinedField({
          id: map.destination,
        });
      if (!companyDefinedField) continue;
      const value = MapperUtils.getValueFromPath(data, map.source);
      if (value) {
        newCustomerCustomFields[companyDefinedField.name] =
          companyDefinedField.dataType == 'string'
            ? value.toString()
            : parseFloat(value);
      } // apenas esta conversão pois no momento estamos permitindo através da interface apenas estes dois tipos, será necessário criar para todos futuramente
    }

    const customer = await this.customersService.findCustomer({
      id: customerId,
    });

    if (!customer) return undefined;

    const existingCompanyDefinedFields = customer.customFields
      ? typeof customer.customFields === 'string'
        ? JSON.parse(customer.customFields)
        : customer.customFields
      : {};

    const updatedCompanyDefinedFields = {
      ...existingCompanyDefinedFields,
      ...newCustomerCustomFields,
    };

    await this.customersService.updateCustomer({
      where: {
        id: customerId,
      },
      data: {
        customFields: updatedCompanyDefinedFields,
      },
    });

    return updatedCompanyDefinedFields;
  }

  private async executeHttpRequest(
    conversation: ConversationWithIncludes,
    targetFlowNode: FlowNodeWithIncludes,
  ): Promise<boolean> {
    const {
      url,
      method,
      headers,
      body,
      dynamicVariables,
      jsonMappings,
      customerCustomFieldMappings,
    } = targetFlowNode.data as HttpRequestNodeData;

    let processedBody = body;
    let invalidBody = false;

    const customer = await this.customersService.findCustomer({
      id: conversation.customerId,
    });

    const customerCustomDefinedFields = customer?.customFields
      ? typeof customer.customFields === 'string'
        ? JSON.parse(customer.customFields)
        : customer.customFields
      : {};
    const uuidRegexTest =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    for (const { value, key } of dynamicVariables || []) {
      if (
        Object.values(UserVariablesEnum).includes(value as UserVariablesEnum)
      ) {
        const customValue = await this.getCustomValueFromEnum(
          value,
          conversation,
        );
        processedBody = processedBody.replace(`[${key}]`, customValue);
      } else if (uuidRegexTest.test(value) && customer) {
        const customField =
          await this.companyDefinedFieldsService.findCompanyDefinedField({
            id: value,
          });
        if (!customField) {
          invalidBody = true;
          break;
        }
        const customValue = customerCustomDefinedFields[customField.name];
        processedBody = processedBody.replace(`[${key}]`, customValue || '');
      }
    }

    const requestHeaders = {
      'Content-Type': 'application/json',
      ...Object.fromEntries(headers?.map((h) => [h.key, h.value]) || []),
    };

    try {
      if (invalidBody) {
        throw new Error('Custom field not found');
      }
      const response = await axios({
        method: method.toLowerCase(),
        url,
        headers: requestHeaders,
        data: JSON.parse(processedBody),
      });
      const mappedResponse =
        jsonMappings != undefined && response?.data != undefined
          ? MapperUtils.mapValuesFromSourceToDestination(
              response?.data,
              jsonMappings,
            )
          : undefined;

      const mappedCustomerCustomFieldMappings = !customerCustomFieldMappings
        ? undefined
        : await this.mapAndSaveCustomerCustomFieldsFromHttpResult(
            response?.data,
            conversation.customerId,
            customerCustomFieldMappings,
          );

      await this.createFlowEvent({
        conversation,
        flowId: targetFlowNode.flowId,
        flowNodeId: targetFlowNode.id,
        flowNodeData: {
          response: response?.data,
          mappedResponse,
          mappedCustomerCustomFieldMappings,
          request: {
            method: method.toLowerCase(),
            url,
            headers: requestHeaders,
            data: JSON.parse(processedBody),
            jsonMappings,
          },
        },
      });

      return true;
    } catch (error: unknown) {
      let errorMessage: string;
      let errorDetails: any = {};

      if (axios.isAxiosError(error)) {
        errorMessage = error.message;
        errorDetails = {
          status: error.response?.status,
          statusText: error.response?.statusText,
          headers: error.response?.headers,
          data: error.response?.data,
          request: {
            method: method.toLowerCase(),
            url,
            headers: requestHeaders,
            data: JSON.parse(processedBody),
          },
        };
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = 'Erro desconhecido';
      }

      await this.createFlowEvent({
        conversation,
        flowId: targetFlowNode.flowId,
        flowNodeId: targetFlowNode.id,
        flowNodeData: {
          error: errorMessage,
          errorDetails,
          request: {
            method: method.toLowerCase(),
            url,
            headers: requestHeaders,
            data: JSON.parse(processedBody),
          },
        },
      });

      return false;
    }
  }

  private async prepareReceiveMessageHandlingContext(params: {
    receiveMessageCommand: ReceiveMessageCommand;
    createdMessage: Message;
  }) {
    const { receiveMessageCommand, createdMessage } = params;
    const conversation = await this.conversationsService.findConversation({
      id: createdMessage.conversationId,
    });

    if (!conversation) {
      throw new Error('Conversa não encontrada');
    }

    if (conversation.customer?.isDeleted) {
      await this.customersService.updateCustomer({
        where: { id: conversation.customerId },
        data: { isDeleted: false },
      });
    }

    const currentOpenTicket = conversation.conversationTickets.at(0);
    const lastSentMessage = conversation.messages.at(0);
    const isNewConversationTicket =
      lastSentMessage && currentOpenTicket
        ? lastSentMessage.createdAt < currentOpenTicket.createdAt
        : true;

    return {
      companyId: receiveMessageCommand.company.id,
      receiveMessageCommand,
      createdMessage,
      conversation,
      currentOpenTicket,
      lastSentMessage,
      isNewConversationTicket,
    };
  }

  private async sendAfterHoursMessage(context: ReceiveMessageHandlingContext) {
    const {
      receiveMessageCommand: { company },
      conversation,
      lastSentMessage,
    } = context;
    const MINIMUN_MINUTES_SINCE_LAST_MESSAGE = 60;

    if (!company.afterHoursMessage) return false;
    if (await this.companiesService.isWorkingHours(company.id, new Date())) {
      return false;
    }
    if (
      !!lastSentMessage &&
      differenceInMinutes(new Date(), lastSentMessage.createdAt) <
        MINIMUN_MINUTES_SINCE_LAST_MESSAGE
    ) {
      return false;
    }

    await this.messagesService.sendMessage({
      isAutomaticResponse: true,
      companyId: company.id,
      senderPhoneNumberId: company.phoneNumberId,
      text: company.afterHoursMessage,
      conversationId: conversation.id,
    });
    return true;
  }

  private async saveFirstReply(context: ReceiveMessageHandlingContext) {
    const { createdMessage, lastSentMessage } = context;

    if (this.shouldSaveFirstReply(context)) {
      await this.messagesService.updateMessage({
        where: {
          id: lastSentMessage!.id,
        },
        data: {
          firstReplyId: createdMessage.id,
        },
      });
    }
  }

  // Normalize text to lowercase, remove accents and extra spaces
  private normalizeText(text: string): string {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/\s+/g, ' ')
      .trim();
  }

  private hasCommonAutoReplyWords(text: string): boolean {
    return AUTO_REPLY_COMMON_WORDS_REGEX.test(text);
  }

  private isSpecialAutoReplyMessage(text: string): boolean {
    return SPECIAL_AUTO_REPLY_PATTERN_REGEX.test(text);
  }

  private containsAutoReplyMessage(text: string): boolean {
    if (this.isSpecialAutoReplyMessage(text)) return true;
    if (!this.hasCommonAutoReplyWords(text)) return false;

    return AUTO_REPLY_SENTENCES.some((sentence) => {
      return text.includes(this.normalizeText(sentence));
    });
  }

  private isAutoReplyMessage(
    text: string,
    createdMessageAt: Date,
    lastSentMessageCreatedAt: Date,
  ): boolean {
    const MINIMUN_SECONDS_BETWEEN_MESSAGES = 8;
    const MAXIMUM_SECONDS_BETWEEN_MESSAGES = 30;
    const normalizedText = this.normalizeText(text);
    const differenceTimeBetweenMessages = differenceInSeconds(
      createdMessageAt,
      lastSentMessageCreatedAt,
    );

    if (differenceTimeBetweenMessages > MAXIMUM_SECONDS_BETWEEN_MESSAGES)
      return false;

    return (
      differenceTimeBetweenMessages <= MINIMUN_SECONDS_BETWEEN_MESSAGES ||
      this.containsAutoReplyMessage(normalizedText)
    );
  }

  private shouldSaveFirstReply(context: ReceiveMessageHandlingContext) {
    const { createdMessage, lastSentMessage } = context;

    return (
      lastSentMessage &&
      lastSentMessage.messageTemplateId &&
      !lastSentMessage.firstReplyId &&
      !this.isAutoReplyMessage(
        createdMessage.text,
        createdMessage.createdAt,
        lastSentMessage.createdAt,
      )
    );
  }

  private async sendFirstContactMessage(
    context: ReceiveMessageHandlingContext,
  ) {
    const {
      receiveMessageCommand: { company },
    } = context;

    if (!this.shouldSendFirstContactMessage(context)) return false;

    if (company.firstContactMessage) {
      const text = await this.replaceParametersFromCustomer(
        company.firstContactMessage,
        context.conversation.customerId,
      );
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: company.id,
        senderPhoneNumberId: company.phoneNumberId,
        text,
        conversationId: context.conversation.id,
      });
    }

    const [flowTrigger] = await this.flowTriggersService.findActiveFlowTriggers(
      {
        flow: {
          type: FlowType.initial_contact,
        },
        companyId: company.id,
        isDefault: true,
      },
      {
        targetFlowNode: {
          include: {
            flowNodeButtons: {
              orderBy: {
                index: 'asc',
              },
            },
          },
        },
      },
    );

    if (!flowTrigger) return true;

    await this.createFlowEvent({
      conversation: context.conversation,
      flowId: flowTrigger.flowId,
      flowTriggerId: flowTrigger.id,
      flowNodeData: {
        type: flowTrigger.type,
        text: flowTrigger.text,
        isDefault: flowTrigger.isDefault,
      },
    });

    return await this.executeFlowNodeAction(
      context.conversation,
      flowTrigger.targetFlowNodeId,
    );
  }

  private shouldSendFirstContactMessage(
    context: ReceiveMessageHandlingContext,
  ) {
    const {
      isNewConversationTicket,
      lastSentMessage,
      receiveMessageCommand: { company },
    } = context;
    const MINIMUN_HOURS_SINCE_LAST_MESSAGE = 12;

    // if (!company.firstContactMessage) return false;
    if (!isNewConversationTicket) return false;
    if (
      !!lastSentMessage &&
      differenceInHours(new Date(), lastSentMessage.createdAt) <
        MINIMUN_HOURS_SINCE_LAST_MESSAGE
    ) {
      return false;
    }
    return true;
  }

  private shouldSendAutoSortingOptions(company: CompanyWithIncludes) {
    return (
      company.isAutomaticSortingActive &&
      company.automaticSortingOptions.length > 0
    );
  }

  private async sendAutoSortingOptionsListMessage({
    company,
    conversation,
    currentOpenTicket,
  }: {
    company: CompanyWithIncludes;
    conversation: ConversationWithIncludes;
    currentOpenTicket?: ConversationTicket;
  }) {
    const conversationCategories = company.automaticSortingOptions.map(
      (option, index) => {
        return `*${index + 1}* - ${option.conversationCategory.name}`;
      },
    );

    await this.messagesService.sendMessage({
      isAutomaticResponse: true,
      companyId: company.id,
      senderPhoneNumberId: company.phoneNumberId,
      text: `${conversationCategories.join('\n')}
*${conversationCategories.length + 1}* - Outros
`,
      conversationId: conversation.id,
    });

    if (currentOpenTicket) {
      await this.conversationTicketService.updateConversationTicket({
        where: {
          id: currentOpenTicket.id,
        },
        data: {
          isWaitingForUserMessage: true,
        },
        notifyUser: false,
        companyId: company.id,
      });
    }
  }

  private async sendAutoSortingOptionMessage(
    context: ReceiveMessageHandlingContext,
  ) {
    const {
      receiveMessageCommand: { company, text },
      currentOpenTicket,
      conversation,
    } = context;

    if (!this.shouldSendAutoSortingOptionMessage(context)) return false;

    const index = Number(text) - 1;
    const orderedOptions =
      await this.automaticSortingOptionsService.listActiveSortingOptionsByCompanyId(
        company.id,
      );
    const selectedOption = orderedOptions[index];
    if (!selectedOption) return false;

    if (currentOpenTicket) {
      await this.conversationTicketService.updateConversationTicket({
        where: {
          id: currentOpenTicket.id,
        },
        data: {
          isWaitingForUserMessage: false,
          categoryId: selectedOption.conversationCategoryId || null,
        },
        notifyUser: false,
        companyId: company.id,
      });

      await this.conversationsService.updateConversation({
        where: {
          id: currentOpenTicket.conversationId,
        },
        data: {
          categoryId: selectedOption.conversationCategoryId || null,
        },
      });
    }

    if (selectedOption.firstMessage) {
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: company.id,
        senderPhoneNumberId: company.phoneNumberId,
        text: selectedOption.firstMessage,
        conversationId: conversation.id,
      });
    }

    if (selectedOption.file) {
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: company.id,
        senderPhoneNumberId: company.phoneNumberId,
        text: selectedOption.file.name,
        conversationId: conversation.id,
        fileKey: selectedOption.file.key,
        mediaType: GupshupFileUtils.getMediaTypeFromMimeType(
          selectedOption.file.mimeType,
        ),
      });
    }
    return true;
  }

  private shouldSendAutoSortingOptionMessage(
    context: ReceiveMessageHandlingContext,
  ) {
    const {
      isNewConversationTicket,
      currentOpenTicket,
      receiveMessageCommand: { text },
    } = context;
    const shouldSendAutoSortingOptionMessage =
      !isNewConversationTicket && currentOpenTicket?.isWaitingForUserMessage;

    if (!shouldSendAutoSortingOptionMessage) return false;
    if (!text) return false;
    return true;
  }

  private async sendAutoReplyMessage(context: ReceiveMessageHandlingContext) {
    const {
      receiveMessageCommand: { company, text },
      conversation,
    } = context;

    if (!text) return false;

    const automaticReply =
      await this.automaticRepliesService.matchMessageWithAutoReply(
        company.id,
        text,
      );
    if (!automaticReply) return false;

    await this.messagesService.sendMessage({
      isAutomaticResponse: true,
      companyId: company.id,
      senderPhoneNumberId: company.phoneNumberId,
      text: automaticReply.message,
      conversationId: conversation.id,
    });
    if (automaticReply?.file) {
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: company.id,
        senderPhoneNumberId: company.phoneNumberId,
        text: automaticReply.file.name,
        conversationId: conversation.id,
        fileKey: automaticReply.file.key,
        mediaType: GupshupFileUtils.getMediaTypeFromMimeType(
          automaticReply.file.mimeType,
        ),
      });
    }

    if (automaticReply.conversationTicketStatus === 'open') {
      await this.conversationsService.updateConversation({
        where: {
          id: conversation.id,
        },
        data: {
          categoryId: automaticReply.conversationCategoryId,
        },
      });
    } else if (automaticReply.conversationTicketStatus === 'closed') {
      await this.conversationsService.updateConversation({
        where: {
          id: conversation.id,
        },
        data: {
          categoryId: automaticReply.conversationCategoryId,
          currentFlowNodeId: null,
          conversationTickets: {
            updateMany: {
              where: {
                status: 'open',
              },
              data: {
                categoryId: automaticReply.conversationCategoryId,
              },
            },
          },
          messages: {
            updateMany: {
              where: {
                status: {
                  not: 'read',
                },
                fromSystem: false,
              },
              data: {
                status: 'read',
              },
            },
          },
        },
      });
    }

    await this.conversationsService.closeAllTickets(conversation.id, {
      shouldUpdateTicketCategory: false,
    });

    return true;
  }

  private async saveMessage(
    receiveMessageCommand: ReceiveMessageCommand,
  ): Promise<Message> {
    const {
      company,
      senderPhoneNumberId,
      senderName,
      text,
      mediaType,
      mediaUrl,
      wamId,
      externalContextMessageId,
    } = receiveMessageCommand;
    const conversation =
      await this.conversationsService.findOrCreateConversation({
        companyId: company.id,
        recipientPhoneNumberId: senderPhoneNumberId,
        recipientName: senderName,
      });

    const flow = await this.resolveConversationFlow(conversation);

    return await this.messagesService.createMessage(
      {
        wamId,
        text: text || '',
        conversationId: conversation.id,
        senderPhoneNumberId: conversation.recipientPhoneNumberId,
        recipientPhoneNumberId: company.phoneNumberId,
        fromSystem: false,
        createdAt: new Date(),
        mediaType: mediaType,
        mediaUrl: mediaUrl,
      },
      {
        externalContextMessageId,
        shouldEmitEvent: this.resolveShouldEmitCreateMessageEvent(flow),
        shouldCreateConversationTicket:
          this.resolveShouldCreateConversationTicket(flow),
      },
    );
  }

  private async executeAssignConversationTicketToAgent(
    conversation: ConversationWithIncludes,
    targetFlowNode: TargetFlowNode,
  ) {
    const { agentIds, overwriteAssignedAgent } =
      targetFlowNode.data as AssignConversationTicketToAgentNodeData;

    const conversationTicket = conversation.conversationTickets[0];

    if (conversationTicket.agentId && overwriteAssignedAgent === false) {
      return;
    }

    const agents = await this.usersService.listUsers(
      {
        where: {
          id: {
            in: agentIds,
          },
          isAgent: true,
          isActive: true,
        },
      },
      {
        skipAiAgents: true,
      },
    );

    if (agents.length === 0) return;

    const randomAgent = ArrayUtils.randomArrayElement(agents);
    if (!randomAgent) return;

    await this.conversationTicketService.updateConversationTicket(
      {
        where: { id: conversationTicket.id },
        companyId: conversation.companyId,
        data: {
          agentId: randomAgent.id,
        },
        notifyUser: true,
      },
      {
        skipAssignDefaultAgent: true,
      },
    );
  }

  async resolveConversationFlow(
    conversation: ConversationWithIncludes,
  ): Promise<Flow | undefined> {
    if (conversation.currentFlowNodeId) {
      return (
        await this.flowNodesService.findFlowNode({
          id: conversation.currentFlowNodeId,
        })
      )?.flow;
    }

    return;
  }

  resolveShouldEmitCreateMessageEvent(flow?: Flow) {
    return flow?.type !== 'csat';
  }

  resolveShouldCreateConversationTicket(flow?: Flow) {
    return flow?.type !== 'csat';
  }
}
